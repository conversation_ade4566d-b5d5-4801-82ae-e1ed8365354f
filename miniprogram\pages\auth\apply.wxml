<view class="container">
  <!-- 温馨提示 -->
  <view class="tips">
    <text class="tips-text">温馨提示：请填写真实信息，我们会尽快审核通过，敬请期待。</text>
  </view>

  <!-- 表单区域 -->
  <view class="form-section">
    <van-cell-group>
      <van-field
        value="{{ selectedHouseText }}"
        label="房号"
        placeholder="请选择小区、楼栋、房屋"
        is-link
        readonly
        required
        bind:tap="showCascader"
        class="form-field"
      />

      <van-field
        value="{{ ownerName }}"
        label="姓名"
        placeholder="请输入姓名"
        required
        bind:change="onOwnerNameChange"
        class="form-field"
      />

      <van-field
        value="{{ idCard }}"
        label="证件号码"
        placeholder="请输入证件号码"
        required
        bind:change="onIdCardChange"
        class="form-field"
      />

      <van-field
        value="{{ mobile }}"
        label="手机号"
        placeholder="请输入手机号"
        type="number"
        maxlength="11"
        required
        bind:change="onMobileChange"
        class="form-field"
      />

      <van-field
        value="{{ genderText }}"
        label="性别"
        placeholder="请选择性别"
        is-link
        readonly
        bind:tap="showGenderPicker"
        class="form-field"
      />

      <van-field
        value="{{ isLiveText }}"
        label="住户状态"
        placeholder="请选择住户状态"
        is-link
        readonly
        bind:tap="showIsLivePicker"
        class="form-field"
      />

      <van-field
        value="{{ moveDate }}"
        label="入住日期"
        placeholder="请选择入住日期"
        is-link
        readonly
        bind:tap="showDatePicker"
        class="form-field"
      />

      <van-field
        value="{{ remark }}"
        label="备注"
        placeholder="请输入备注信息（选填）"
        type="textarea"
        autosize
        bind:change="onRemarkChange"
        class="form-field"
      />
    </van-cell-group>

    <!-- 认证身份 -->
    <view class="rel-type-section">
      <view class="rel-type-title">认证身份 <text class="required">*</text></view>
      <van-radio-group value="{{ relType }}" bind:change="onRelTypeChange">
        <van-cell-group>
          <van-cell title="业主" clickable data-name="1" bind:click="onRelTypeClick">
            <van-radio slot="right-icon" name="1" />
          </van-cell>
          <van-cell title="家庭成员" clickable data-name="2" bind:click="onRelTypeClick">
            <van-radio slot="right-icon" name="2" />
          </van-cell>
          <van-cell title="租户" clickable data-name="3" bind:click="onRelTypeClick">
            <van-radio slot="right-icon" name="3" />
          </van-cell>
        </van-cell-group>
      </van-radio-group>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-section">
    <van-button
      type="primary"
      size="large"
      class="submit-btn {{canSubmit ? '' : 'disabled'}}"
      loading="{{ submitting }}"
      bind:click="submitApply"
    >
      {{ submitting ? '提交中...' : '确定' }}
    </van-button>
  </view>
</view>

<!-- 级联选择器弹窗 -->
<van-popup
  show="{{ showCascaderPopup }}"
  position="bottom"
  round
  custom-class="cascader-popup"
  bind:close="onClose"
>
  <van-cascader
    wx:if="{{ showCascaderPopup }}"
    value="{{ cascaderValue }}"
    title="选择房屋"
    options="{{ cascaderOptions }}"
    bind:close="onClose"
    bind:change="onCascaderChange"
    bind:finish="onCascaderFinish"
  />
</van-popup>

<!-- 性别选择器弹窗 -->
<van-popup
  show="{{ showGenderPicker }}"
  position="bottom"
  round
  custom-class="picker-popup"
  bind:close="onGenderPickerClose"
>
  <van-picker
    title="选择性别"
    columns="{{ genderOptions }}"
    bind:confirm="onGenderConfirm"
    bind:cancel="onGenderPickerClose"
  />
</van-popup>

<!-- 住户状态选择器弹窗 -->
<van-popup
  show="{{ showIsLivePicker }}"
  position="bottom"
  round
  custom-class="picker-popup"
  bind:close="onIsLivePickerClose"
>
  <van-picker
    title="选择住户状态"
    columns="{{ isLiveOptions }}"
    bind:confirm="onIsLiveConfirm"
    bind:cancel="onIsLivePickerClose"
  />
</van-popup>

<!-- 入住日期选择器弹窗 -->
<van-popup
  show="{{ showDatePicker }}"
  position="bottom"
  round
  custom-class="picker-popup"
  bind:close="onDatePickerClose"
>
  <van-datetime-picker
    type="date"
    title="选择入住日期"
    value="{{ currentDate }}"
    bind:confirm="onDateConfirm"
    bind:cancel="onDatePickerClose"
  />
</van-popup>


